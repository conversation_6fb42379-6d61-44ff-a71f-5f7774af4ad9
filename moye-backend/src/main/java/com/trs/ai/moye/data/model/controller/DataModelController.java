package com.trs.ai.moye.data.model.controller;

import com.trs.ai.moye.common.entity.UsageInfoResponse;
import com.trs.ai.moye.data.model.entity.MonitorVersionQuery;
import com.trs.ai.moye.data.model.entity.StorageTask;
import com.trs.ai.moye.data.model.request.AllMonitorConfigRequest;
import com.trs.ai.moye.data.model.request.CreateTableRequests;
import com.trs.ai.moye.data.model.request.DataLineageRequest;
import com.trs.ai.moye.data.model.request.DataModelSearchRequest;
import com.trs.ai.moye.data.model.request.DataModelUpdateRequest;
import com.trs.ai.moye.data.model.request.DataSourceModelRequest;
import com.trs.ai.moye.data.model.request.DataStorageUpdateParam;
import com.trs.ai.moye.data.model.request.DwdAddRequest;
import com.trs.ai.moye.data.model.request.FieldExportRequest;
import com.trs.ai.moye.data.model.request.ModelFieldRequest;
import com.trs.ai.moye.data.model.request.MonitorConfigVersionRequest;
import com.trs.ai.moye.data.model.request.MoveRequest;
import com.trs.ai.moye.data.model.request.ReadErrorMessagesRequest;
import com.trs.ai.moye.data.model.request.SaveGovernanceInfoRequest;
import com.trs.ai.moye.data.model.request.ScheduleRecordRequest;
import com.trs.ai.moye.data.model.request.fields.ConnectionTableFieldsRequest;
import com.trs.ai.moye.data.model.request.fields.FileFieldsRequest;
import com.trs.ai.moye.data.model.request.fields.HttpFieldsRequest;
import com.trs.ai.moye.data.model.request.fields.SourceModelFieldsRequest;
import com.trs.ai.moye.data.model.request.imports.FieldDdlImportRequest;
import com.trs.ai.moye.data.model.request.imports.FieldJsonImportRequest;
import com.trs.ai.moye.data.model.request.reverse.ReverseModelingRequest;
import com.trs.ai.moye.data.model.response.BacklogResponse;
import com.trs.ai.moye.data.model.response.BatchDeleteResponse;
import com.trs.ai.moye.data.model.response.CheckExistedTableFieldsResponse;
import com.trs.ai.moye.data.model.response.CreateTableResponse;
import com.trs.ai.moye.data.model.response.DataLineageVO;
import com.trs.ai.moye.data.model.response.DataModelSourceDetailResponse;
import com.trs.ai.moye.data.model.response.DataModelStatusResponse;
import com.trs.ai.moye.data.model.response.DataSourceModelResponse;
import com.trs.ai.moye.data.model.response.DwdAddResponse;
import com.trs.ai.moye.data.model.response.FieldPageListResponse;
import com.trs.ai.moye.data.model.response.FieldParseResponse;
import com.trs.ai.moye.data.model.response.KafkaBacklogResponse;
import com.trs.ai.moye.data.model.response.ModelBasicInfoResponse;
import com.trs.ai.moye.data.model.response.ModelDataStorageResponse;
import com.trs.ai.moye.data.model.response.ModelMonitorConfigResponse;
import com.trs.ai.moye.data.model.response.MonitorConfigVersionResponse;
import com.trs.ai.moye.data.model.response.OdsDataSourceResponse;
import com.trs.ai.moye.data.model.response.ReverseModelingResponse;
import com.trs.ai.moye.data.model.response.ScheduleConfigResponse;
import com.trs.ai.moye.data.model.service.DataModelCategoryService;
import com.trs.ai.moye.data.model.service.DataModelScheduleService;
import com.trs.ai.moye.data.model.service.DataModelService;
import com.trs.ai.moye.data.model.service.DwdModelService;
import com.trs.ai.moye.data.model.service.ImportService;
import com.trs.ai.moye.data.model.service.OdsModelService;
import com.trs.ai.moye.data.model.service.impl.DataLineAgeService;
import com.trs.ai.moye.data.model.service.impl.DataModelFieldService;
import com.trs.ai.moye.data.model.service.impl.DataModelStorageService;
import com.trs.ai.moye.data.model.service.impl.ReverseModelingService;
import com.trs.ai.moye.data.standard.dao.MetaDataStandardMapper;
import com.trs.ai.moye.data.standard.entity.GraphicsMetaDataStandard;
import com.trs.ai.moye.data.standard.response.MetaDataStandardDetailResponse;
import com.trs.ai.moye.permission.service.DynamicUserNameService;
import com.trs.ai.moye.storageengine.dto.MonitorDTO;
import com.trs.ai.moye.storageengine.feign.MonitorFeign;
import com.trs.ai.moye.storageengine.request.ConditionSearchParams;
import com.trs.ai.moye.storageengine.response.StorageSearchResponse;
import com.trs.moye.base.common.annotaion.OperateLogSign;
import com.trs.moye.base.common.entity.ModuleEnum;
import com.trs.moye.base.common.enums.ModelLayer;
import com.trs.moye.base.common.exception.BizException;
import com.trs.moye.base.common.help.RepeatChecker;
import com.trs.moye.base.common.log.operate.OperateType;
import com.trs.moye.base.common.response.PageResponse;
import com.trs.moye.base.data.execute.ExecuteParams;
import com.trs.moye.base.data.model.dao.DataModelExecuteConfigMapper;
import com.trs.moye.base.data.model.dao.DataModelMapper;
import com.trs.moye.base.data.model.entity.DataModel;
import com.trs.moye.base.data.model.entity.DataModelExecuteConfig;
import com.trs.moye.base.data.model.entity.DataModelField;
import com.trs.moye.base.data.model.enums.CreateTableStatus;
import com.trs.moye.base.data.model.field.mapping.MoyeFieldResponse;
import com.trs.moye.base.data.schedule.ScheduleInfo;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import lombok.extern.slf4j.Slf4j;
import org.springframework.lang.Nullable;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

/**
 * 数据建模
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024/9/26 14:14
 **/
@Slf4j
@RestController
@RequestMapping("/data-model")
@Validated
public class DataModelController {

    @Resource
    private DataModelService dataModelService;

    @Resource
    private DataModelMapper dataModelMapper;

    @Resource
    private MetaDataStandardMapper metaDataStandardMapper;

    @Resource
    private DynamicUserNameService dynamicUserNameService;

    @Resource
    private ReverseModelingService reverseModelingService;

    @Resource
    private OdsModelService odsModelService;

    @Resource
    private DwdModelService dwdModelService;

    @Resource
    private ImportService importService;

    @Resource
    private DataModelStorageService dataModelStorageService;

    @Resource
    private DataModelScheduleService dataModelScheduleService;

    @Resource
    private MonitorFeign monitorFeign;

    @Resource
    private DataModelFieldService dataModelFieldService;

    @Resource
    private DataModelCategoryService dataModelCategoryService;

    @Resource
    private DataLineAgeService dataLineAgeService;

    @Resource
    private DataModelExecuteConfigMapper dataModelExecuteConfigMapper;


    /**
     * 批量移动层级 <a href="http://**************:3001/project/5419/interface/api/164170">...</a>
     *
     * @param request 前端请求
     * @return {@link Boolean}
     * <AUTHOR>
     * @since 2024/9/29 15:43
     */
    @OperateLogSign(module = ModuleEnum.DATA_MODELING, apiName = "数据建模批量移动层级")
    @PostMapping("/batch-move")
    public boolean batchMove(@RequestBody @Valid MoveRequest request) {
        if (Objects.isNull(request.getIds()) || request.getIds().isEmpty()) {
            return Boolean.FALSE;
        }
        return dataModelService.moveMetaDataToCategory(request.getIds(), request.getBusinessId());

    }


    /**
     * 删除数据建模  <a href="http://**************:3001/project/5419/interface/api/164350">...</a>
     *
     * @param ids x需要删除掉的id
     * @return {@link BatchDeleteResponse}
     * <AUTHOR>
     * @since 2024/9/29 16:22
     */
    @OperateLogSign(module = ModuleEnum.DATA_MODELING, apiName = "批量删除数据建模")
    @DeleteMapping("/batch-delete")
    public List<BatchDeleteResponse> deleteDataModel(@RequestBody @NotEmpty List<Integer> ids) {
        return dataModelService.deleteDataModel(ids);
    }


    /**
     * 数据建模基本信息 <a href="https://yapi-192.trscd.com.cn/project/5419/interface/api/164295">...</a>
     *
     * @param id 建模id
     * @return 建模基本信息
     */
    @GetMapping("/{id}/basic")
    public ModelBasicInfoResponse modelBasicInfo(@PathVariable Integer id) {
        return dataModelService.getModelBasicInfo(id);
    }

    /**
     * 修改建模基本信息 <a href="https://yapi-192.trscd.com.cn/project/5419/interface/api/164795">...</a>
     *
     * @param id      建模id
     * @param request 修改请求
     */
    @OperateLogSign(module = ModuleEnum.DATA_MODELING, apiName = "修改建模基本信息")
    @PutMapping("/{id}/basic")
    public void updateBasicInfo(@PathVariable Integer id, @RequestBody DataModelUpdateRequest request) {
        dataModelService.updateModelZhName(id, request);
    }

    /**
     * 数据建模的数据源信息 <a href="https://yapi-192.trscd.com.cn/project/5419/interface/api/164305">...</a>
     *
     * @param id 建模id
     * @return 数据源信息
     */
    @GetMapping("/{id}/data-source")
    public OdsDataSourceResponse modelDataSource(@PathVariable Integer id) {
        return odsModelService.getOdsDataSource(id);
    }

    /**
     * 数据建模的存储点信息 <a href="https://yapi-192.trscd.com.cn/project/5419/interface/api/164310">...</a>
     *
     * @param id                建模id
     * @param createTableStatus 创建表状态
     * @return 存储点信息列表
     */
    @GetMapping("/{id}/data-storage")
    public List<ModelDataStorageResponse> modelDataStorages(@PathVariable Integer id,
        @RequestParam(required = false) CreateTableStatus createTableStatus) {

        return dataModelService.getModelDataStorages(id).stream().filter(
            modelDataStorageResponse -> Objects.isNull(createTableStatus) || createTableStatus.equals(
                modelDataStorageResponse.getCreateTableStatus())).toList();
    }

    /**
     * 修改建模存储点 <a href="https://yapi-192.trscd.com.cn/project/5419/interface/api/164835">...</a>
     *
     * @param id      建模id
     * @param request 请求参数
     */
    @OperateLogSign(module = ModuleEnum.DATA_MODELING, apiName = "修改建模存储点")
    @PutMapping("/{id}/data-storage")
    public void updateDataStorages(@PathVariable Integer id,
        @RequestBody @Validated List<DataStorageUpdateParam> request) {
        dataModelStorageService.updateDataStorages(id, request);
    }


    /**
     * 数据建模的调度配置信息 <a href="https://yapi-192.trscd.com.cn/project/5419/interface/api/164315">...</a>
     *
     * @param id 建模id
     * @return 调度配置信息
     */
    @GetMapping("/{id}/schedule-config")
    public ScheduleConfigResponse modelScheduleConfig(@PathVariable Integer id) {
        return dataModelScheduleService.getModelScheduleInfo(id);
    }

    /**
     * 修改调度配置 <a href="http://**************:3001/project/5419/interface/api/164645">...</a>
     *
     * @param id           建模id
     * @param scheduleInfo {@link ScheduleInfo} 调度信息
     */
    @OperateLogSign(module = ModuleEnum.DATA_MODELING, apiName = "修改调度配置")
    @PutMapping("/{id}/schedule-config")
    public void updateScheduleConfig(@PathVariable Integer id, @RequestBody ScheduleInfo scheduleInfo) {
        dataModelScheduleService.updateModelScheduleConfig(id, scheduleInfo);
    }

    /**
     * 数据建模的监控配置信息 <a href="https://yapi-192.trscd.com.cn/project/5419/interface/api/164320">...</a>
     *
     * @param id 建模id
     * @return 监控配置
     */
    @GetMapping("/{id}/monitor-config")
    public ModelMonitorConfigResponse modelMonitorConfigInfo(@PathVariable Integer id) {
        return dataModelService.getModelMonitorConfigInfo(id);
    }


    /**
     * 修改监控配置
     *
     * @param id      建模id
     * @param request 请求参数
     */
    @OperateLogSign(module = ModuleEnum.DATA_MODELING, apiName = "修改监控配置")
    @PutMapping("/{id}/monitor-config")
    public void updateMonitorConfigInfo(@PathVariable Integer id, @RequestBody AllMonitorConfigRequest request) {
        request.validate();
        dataModelService.updateMonitorConfigInfo(id, request);
    }

    /**
     * 修改监控配置
     *
     * @param id      建模id
     * @param request 请求参数
     * @return {@link MonitorConfigVersionResponse}
     */
    @PostMapping("/{id}/monitor-config/version/page-list")
    public PageResponse<MonitorConfigVersionResponse> monitorTypeVersionPageList(@PathVariable Integer id,
        @Validated @RequestBody MonitorConfigVersionRequest request) {
        return dataModelService.monitorTypeVersionPageList(id, new MonitorVersionQuery(request));
    }

    /**
     * 执行配置 <a href="https://yapi-192.trscd.com.cn/project/5419/interface/api/164590">...</a>
     *
     * @param id 建模id
     * @return 执行配置信息
     */
    @GetMapping("/{id}/status")
    public DataModelStatusResponse modelStatus(@PathVariable Integer id) {
        return dataModelService.modelStatus(id);
    }

    /**
     * <br>
     * <a href="https://yapi-192.trscd.com.cn/project/5419/interface/api/164985">YApi</a>
     *
     * @param id 数据建模id
     * @return 数据建模 使用的 元数据标准，如果没有使用则返回null
     */
    @Nullable
    @GetMapping("/{id}/meta-data-standard")
    public MetaDataStandardDetailResponse modelMetaDataStandard(@PathVariable Integer id) {
        GraphicsMetaDataStandard metaDataStandard = metaDataStandardMapper.selectByDataModelId(id);
        if (Objects.isNull(metaDataStandard)) {
            return null;
        }
        return MetaDataStandardDetailResponse.from(metaDataStandard, dynamicUserNameService);
    }

    /**
     * 数据建模字段分页列表 <a href="https://yapi-192.trscd.com.cn/project/5419/interface/api/164325">...</a>
     *
     * @param id 数建模id
     * @return 字段分页信息
     */
    @GetMapping("/{id}/field/page-list")
    public List<FieldPageListResponse> modelFieldPageList(@PathVariable Integer id) {
        return dataModelService.modelFieldPageList(id);
    }

    /**
     * 数据建模字段列表 <a href="https://yapi-192.trscd.com.cn/project/5419/interface/api/164130">...</a>
     *
     * @param id               数建模id
     * @param isProvideService 是否提供服务
     * @return 字段列表
     */
    @GetMapping("/{id}/field/list")
    public List<FieldPageListResponse> modelFieldList(@PathVariable Integer id,
        @RequestParam(required = false) Boolean isProvideService) {
        return dataModelFieldService.getModelFieldListWithKnowledgeBase(id, isProvideService);
    }

    /**
     * 保存数据建模字段列表
     *
     * @param id          建模id
     * @param requestList 字段列表
     */
    @OperateLogSign(module = ModuleEnum.DATA_MODELING, apiName = "保存数据建模字段列表")
    @PostMapping("/{id}/field/list")
    public void saveFieldList(@PathVariable Integer id,
        @Validated @NotEmpty(message = "字段列表不能为空") @RequestBody List<ModelFieldRequest> requestList) {
        // 检查字段英文名重复
        RepeatChecker.checkCollection(requestList, "以下字段英文名存在重复：\n", ModelFieldRequest::getEnName);
        // 检查 图形化数据建模 图形化标签/边 的 字段重复 和 主键情况
        requestList.forEach(ModelFieldRequest::checkFields);
        dataModelFieldService.saveFieldList(id, requestList);
    }

    /**
     * 翻译元数据字段中文名和描述
     *
     * @param id     建模id
     * @param fields 字段列表
     * @return 字段列表
     */
    @PostMapping("/{id}/field/translate")
    public List<DataModelField> translateFields(@PathVariable Integer id, @RequestBody List<DataModelField> fields) {
        return dataModelFieldService.translateFields(id, fields);
    }

    /**
     * 同步元数据标准字段信息，包含中文名描述等 <a href="http://**************:3001/project/5419/interface/api/168587">...</a>
     *
     * @param id 数建模id
     * @return 字段列表
     */
    @GetMapping("/{id}/standard-field/syn")
    public boolean standardFieldSyn(@PathVariable Integer id) {
        return dataModelFieldService.standardFieldSyn(id);
    }


    /**
     * 获取要素库数据建模中的数据来源 <a href="http://**************:3001/project/5419/interface/api/164850">...</a>
     *
     * @param request 请求
     * @return {@link DataSourceModelResponse}
     * <AUTHOR>
     * @since 2024/10/11 17:26
     */
    @PostMapping("/data-source/card-list")
    public List<DataSourceModelResponse> getModelDataSourceList(@RequestBody @Valid DataSourceModelRequest request) {
        return dataModelService.getModelDataSourceList(request);
    }

    /**
     * 获取数据建模基础信息列表
     *
     * @param request 请求
     * @return {@link List }<{@link ModelBasicInfoResponse }>
     * <AUTHOR>
     * @since 2025/05/22 11:25:52
     */
    @PostMapping("/basic-list")
    public List<ModelBasicInfoResponse> getDataModelBasicList(@RequestBody @Valid DataModelSearchRequest request) {
        return dataModelCategoryService.getModelBasicInfoList(request);
    }

    /**
     * 新增要素库、主题库、专题库 建模  <a href="http://**************:3001/project/5419/interface/api/164240">...</a>
     *
     * @param request 前端请求
     * @return {@link DwdAddResponse}
     * <AUTHOR>
     * @since 2024/9/27 18:05
     */
    @OperateLogSign(module = ModuleEnum.DATA_MODELING, apiName = "新增要素库、主题库、专题库 建模")
    @PostMapping()
    public DwdAddResponse addModel(@RequestBody DwdAddRequest request) {
        return dwdModelService.addModel(request);
    }

    /**
     * 保存数据治理相关信息
     *
     * @param dataModelId 数据建模id
     * @param request     治理信息保存请求参数
     * @throws BizException 业务异常
     * <AUTHOR>
     * @since 2024/12/20 15:11:22
     */
    @OperateLogSign(module = ModuleEnum.DATA_MODELING, apiName = "保存数据治理相关信息")
    @PostMapping("/{dataModelId}/save-governance-info")
    public void saveGovernanceInfo(@PathVariable Integer dataModelId,
        @RequestBody @Validated SaveGovernanceInfoRequest request) {
        dwdModelService.saveGovernanceInfo(dataModelId, request);
    }

    /**
     * 删除建模 要素库、主题库、专题库 <a href="http://**************:3001/project/5419/interface/api/164735">...</a>
     *
     * @param id 建模id
     * @return {@link Boolean}
     * <AUTHOR>
     * @since 2024/10/10 10:44
     */
    @OperateLogSign(module = ModuleEnum.DATA_MODELING, apiName = "删除建模 要素库、主题库、专题库")
    @DeleteMapping("/{id}")
    public boolean deleteModel(@PathVariable Integer id) {
        return dataModelService.deleteModel(id);
    }


    /**
     * 要素库、主题库、专题库 建模被引用使用情况 <a href="http://**************:3001/project/5419/interface/api/164745">...</a>
     *
     * @param id 数据建ID
     * @return {@link String}
     * <AUTHOR>
     * @since 2024/10/10 14:13
     */
    @GetMapping("/{id}/usage")
    public UsageInfoResponse getModelUsageInfo(@PathVariable @NotNull(message = "id不能为空！") Integer id) {
        return dataModelService.getModelUsageInfo(id);
    }

    /**
     * <a href="http://**************:3001/project/5419/interface/api/164095">创建表</a>
     *
     * @param dataModelId         数据模型id
     * @param createTableRequests 创建表请求
     * @return 是否成功
     */
    @OperateLogSign(module = ModuleEnum.DATA_MODELING, apiName = "建模创建表")
    @PostMapping("/{dataModelId}/create-table")
    public List<CreateTableResponse> createTable(@PathVariable Integer dataModelId,
        @RequestBody CreateTableRequests createTableRequests) {
        return dataModelService.createTable(dataModelId, createTableRequests);
    }


    /**
     * 校验dataModel的英文名是否重复 <a href="http://**************:3001/project/5419/interface/api/164230">...</a>
     *
     * @param enName 英文名
     * @return {@link Boolean}
     * <AUTHOR>
     * @since 2024/10/14 10:32
     */
    @GetMapping("/check-en-name")
    public Boolean existsByEnName(@RequestParam @NotBlank(message = "英文名不能为空！") String enName) {
        return dataModelMapper.existsByEnName(enName);
    }


    /**
     * 获取某个（要素库、主题库、专题库）数据建模的数据来源信息 <a href="http://**************:3001/project/5419/interface/api/164930">...</a>
     *
     * @param id 数据建模ID
     * @return {@link DataModelSourceDetailResponse}
     * <AUTHOR>
     * @since 2024/10/14 14:57
     */
    @GetMapping("/data-source/{id}/detail")
    public List<DataModelSourceDetailResponse> getDataModelSourceDetail(@PathVariable Integer id) {
        return dataModelService.getDataModelSourceDetail(id);
    }

    /**
     * 数据预览分页列表
     *
     * @param dataModelId 数据建模ID
     * @param storageId   存储ID
     * @param request     请求参数
     * @return {@link StorageSearchResponse}
     */
    @PostMapping("/{dataModelId}/data-preview/page-list")
    public PageResponse<Map<String, Object>> getDataPreviewPageList(@PathVariable("dataModelId") Integer dataModelId,
        @RequestParam(value = "storageId", required = false) Integer storageId,
        @RequestBody @Valid ConditionSearchParams request) {
        return dataModelService.getDataPreviewPageList(dataModelId, storageId, request);
    }

    /**
     * 查询未读的错误调度信息  <a href="http://**************:3001/project/5419/interface/api/165095">...</a>
     *
     * @param dataModelId 数据建模ID
     * @return {@link Integer}
     * <AUTHOR>
     * @since 2024/10/21 16:42
     */
    @GetMapping("/{dataModelId}/schedule-record/error-count")
    public Integer getErrorCount(@NotNull @PathVariable Integer dataModelId) {
        return dataModelService.getErrorCount(dataModelId);
    }


    /**
     * 错误信息已读 <a href="http://**************:3001/project/5419/interface/api/165100">...</a>
     *
     * @param id      单条数据的主键ID
     * @param request 前端参数
     * @return {@link Boolean}
     * <AUTHOR>
     * @since 2024/10/21 17:27
     */
    @PutMapping("/{id}/read/error-messages")
    public Boolean readErrorMessages(@NotNull @PathVariable String id,
        @RequestBody @Validated ReadErrorMessagesRequest request) {
        return dataModelService.readErrorMessages(id, request);
    }

    /**
     * 逆向建模
     * <a href="http://**************:3001/project/5419/interface/api/164165">逆向建模</a>
     *
     * @param request 请求
     * @return {@link List }<{@link ReverseModelingResponse }>
     * <AUTHOR>
     * @since 2024/10/23 15:30:15
     */
    @OperateLogSign(module = ModuleEnum.DATA_MODELING, apiName = "逆向建模")
    @PostMapping("/reverse-modeling")
    public List<ReverseModelingResponse> reverseModeling(@RequestBody @Validated ReverseModelingRequest request) {
        return reverseModelingService.reverseModelingBatch(request);
    }

    /**
     * 逆向建模，供batch-engine使用
     * <a href="http://**************:3001/project/5419/interface/api/165425">根据建模id逆向建模（batch-engine使用）</a>
     *
     * @param modelId 数据建模ID
     * @throws BizException 业务异常类
     * <AUTHOR>
     * @since 2024/11/06 11:31:17
     */
    @PostMapping("/reverse-modeling/{modelId}")
    public void reverseModelingAuto(@PathVariable Integer modelId) throws BizException {
        dataModelService.reverseModelingByModelId(modelId);
    }

    /**
     * <a href=http://**************:3001/project/4220/interface/api/162665>查询数据血缘</a>
     *
     * @param id      表id
     * @param request 时间范围参数
     * @return 血缘关系 {@link DataLineageVO}<AUTHOR>
     * @since 2025/05/13 16:24:30
     */
    @PostMapping("/{id}/lineage")
    public DataLineageVO getDataLineage(@PathVariable Integer id,
        @RequestBody DataLineageRequest request) {
        return dataLineAgeService.getDataLineage(id, request);
    }

    /**
     * 导出建模字段
     *
     * @param id       数据建模ID
     * @param request  请求参数
     * @param response http响应
     */
    @OperateLogSign(module = ModuleEnum.DATA_MODELING, operateType = OperateType.EXPORT, apiName = "导出建模字段")
    @PostMapping("/{id}/export/field")
    public void exportFields(@PathVariable("id") Integer id, @RequestBody FieldExportRequest request,
        HttpServletResponse response) {
        dataModelService.exportFields(id, request, response);
    }

    /**
     * 同步帖源库字段 <a href="https://yapi-192.trscd.com.cn/project/5419/interface/api/164825">...</a>
     *
     * @param id 建模id
     */
    @OperateLogSign(module = ModuleEnum.DATA_MODELING, apiName = "同步帖源库字段")
    @PutMapping("/{id}/sync-fields")
    public void syncOdsFields(@PathVariable Integer id) {
        dataModelService.syncOdsFields(id);
    }

    /**
     * 获取帖源库需要同步的字段 <a href="https://yapi-192.trscd.com.cn/project/5419/interface/api/164070">...</a>
     *
     * @param id 帖源库id
     * @return 需要同步的字段列表
     */
    @GetMapping("/{id}/sync-fields")
    public List<MoyeFieldResponse> getOdsNeedSyncFields(@PathVariable Integer id) {
        return dataModelService.getOdsNeedSyncFields(id);
    }

    /**
     * 从元数据标准同步字段到数据建模
     *
     * @param id                 数据建模id
     * @param metaDataStandardId 元数据标准id
     * <AUTHOR>
     * @since 2024/12/20 15:11:22
     */
    @OperateLogSign(module = ModuleEnum.DATA_MODELING, apiName = "从元数据标准同步字段")
    @PostMapping("/{id}/sync-fields/{metaDataStandardId}")
    public void syncFieldsFromMetaDataStandard(@PathVariable Integer id, @PathVariable Integer metaDataStandardId) {

        dwdModelService.syncFieldsFromMetaDataStandard(id, metaDataStandardId);
    }

    /**
     * 解析json文件字段
     *
     * @param id      数据建模ID
     * @param request 请求参数
     * @return {@link FieldParseResponse }
     */
    @PostMapping("/{id}/parse-field/json")
    public FieldParseResponse parseJsonFields(@PathVariable("id") Integer id,
        @ModelAttribute FieldJsonImportRequest request) {
        return importService.parseJsonFields(id, request);
    }

    /**
     * 解析excel文件字段
     *
     * @param id   数据建模ID
     * @param file excel文件
     * @return {@link FieldParseResponse }
     */
    @PostMapping("/{id}/parse-field/excel")
    public FieldParseResponse parseExcelFields(@PathVariable("id") Integer id, @RequestParam MultipartFile file) {
        return importService.parseExcelFields(id, file);
    }

    /**
     * 解析ddl文件字段
     *
     * @param id      数据建模ID
     * @param request 请求参数
     * @return {@link FieldParseResponse }
     */
    @PostMapping("/{id}/parse-field/ddl")
    public FieldParseResponse parseDdlFields(@PathVariable("id") Integer id,
        @ModelAttribute FieldDdlImportRequest request) {
        return importService.parseDdlFields(id, request);
    }

    /**
     * 检查数据库存在字段
     * <a href="http://**************:3001/project/5419/interface/api/166219">【数据建模】检查db来源和已有表</a>
     *
     * @param request 请求
     * @return {@link CheckExistedTableFieldsResponse }
     * <AUTHOR>
     * @since 2024/12/16 10:56:39
     */
    @PostMapping("/db/check-existence/table-fields")
    public CheckExistedTableFieldsResponse checkDbExistenceFields(
        @RequestBody @Validated ConnectionTableFieldsRequest request) {
        return dwdModelService.checkExistedTableFields(request);
    }

    /**
     * 检查 MQ 存在字段
     * <a href="http://**************:3001/project/5419/interface/api/166225">【数据建模】检查mq来源和已有表</a>
     *
     * @param request 请求
     * @return {@link CheckExistedTableFieldsResponse }
     * <AUTHOR>
     * @since 2024/12/16 10:56:42
     */
    @PostMapping("/mq/check-existence/table-fields")
    public CheckExistedTableFieldsResponse checkMqExistenceFields(
        @RequestBody @Validated ConnectionTableFieldsRequest request) {
        return dwdModelService.checkExistedTableFields(request);
    }

    /**
     * 检查文件存在字段
     * <a href="http://**************:3001/project/5419/interface/api/166231">【数据建模】检查file来源和已有表</a>
     *
     * @param request 请求
     * @return {@link CheckExistedTableFieldsResponse }
     * <AUTHOR>
     * @since 2024/12/16 10:56:45
     */
    @PostMapping("/file/check-existence/table-fields")
    public CheckExistedTableFieldsResponse checkFileExistenceFields(@RequestBody @Validated FileFieldsRequest request) {
        return dwdModelService.checkExistedTableFields(request);
    }

    /**
     * 检查 HTTP 存在字段
     * <a href="http://**************:3001/project/5419/interface/api/166237">【数据建模】检查http来源和已有表</a>
     *
     * @param request 请求
     * @return {@link CheckExistedTableFieldsResponse }
     * <AUTHOR>
     * @since 2024/12/16 10:56:49
     */
    @PostMapping("/http/check-existence/table-fields")
    public CheckExistedTableFieldsResponse checkHttpExistenceFields(@RequestBody @Validated HttpFieldsRequest request) {
        return dwdModelService.checkExistedTableFields(request);
    }

    /**
     * 检查数据建模存在字段
     *
     * @param request 请求
     * @return {@link CheckExistedTableFieldsResponse }
     * <AUTHOR>
     * @since 2024/12/16 10:56:47
     */
    @PostMapping("/model/check-existence/table-fields")
    public CheckExistedTableFieldsResponse checkModelExistenceFields(
        @RequestBody @Validated SourceModelFieldsRequest request) {
        return dwdModelService.checkExistedTableFields(request);
    }

    /**
     * 调度监控分页查询接口
     * <a href="http://**************:3001/project/5419/interface/api/164115">...</a>
     *
     * @param dataModelId   数据建模id
     * @param requestParams 请求参数
     * @return {@link StorageTask}
     */
    @PostMapping("/{dataModelId}/schedule-record/page-list")
    public PageResponse<StorageTask> getScheduleRecordPageList(@PathVariable Integer dataModelId,
        @RequestBody ScheduleRecordRequest requestParams) {
        return dataModelService.getScheduleRecordPageList(dataModelId, requestParams);
    }


    /**
     * 获取数据建模数据积压信息 <a href="http://**************:3001/project/5419/interface/api/167482">...</a>
     *
     * @param id 建模ID
     * @return 需要同步的字段列表
     */
    @GetMapping("/{id}/backlog")
    public BacklogResponse getBacklog(@PathVariable @NotNull Integer id) {
        DataModel dataModel = dataModelMapper.selectById(id);
        if (Objects.isNull(dataModel)) {
            throw new BizException("数据建模不存在，dataModelId:{}", id);
        }

        MonitorDTO monitorDTO = new MonitorDTO(id);
        ModelLayer layer = dataModel.getLayer();
        if (ModelLayer.ODS.equals(layer)) {
            Long count = monitorFeign.getDataSourceLag(monitorDTO);
            return new BacklogResponse(Collections.singletonList(new KafkaBacklogResponse("", count)),
                LocalDateTime.now());
        } else {
            List<KafkaBacklogResponse> kafkaBacklogInfo = monitorFeign.getKafkaBacklogInfo(monitorDTO);
            return new BacklogResponse(kafkaBacklogInfo, LocalDateTime.now());
        }
    }

    /**
     * 获取数据建模执行参数
     *
     * @param id 数据建模ID
     * @return {@link ExecuteParams}
     */
    @GetMapping("/{id}/execute-config")
    public DataModelExecuteConfig getExecuteConfigs(@PathVariable Integer id) {
        return dataModelExecuteConfigMapper.selectByDataModelId(id);
    }


}

