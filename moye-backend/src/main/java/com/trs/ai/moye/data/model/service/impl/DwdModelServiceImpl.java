package com.trs.ai.moye.data.model.service.impl;


import static com.trs.ai.moye.data.model.service.impl.OdsCreateService.TRS_MOYE_FTP_FILE_NAME;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.trs.ai.moye.backstage.dao.NoticeSendConfInsideMapper;
import com.trs.ai.moye.backstage.entity.NoticeSendConfInside;
import com.trs.ai.moye.common.utils.MinioUtil;
import com.trs.ai.moye.data.model.checker.CheckTableFieldsStrategy;
import com.trs.ai.moye.data.model.checker.CheckTableFieldsStrategyFactory;
import com.trs.ai.moye.data.model.dao.BatchArrangementMapper;
import com.trs.ai.moye.data.model.dao.BatchTaskRecordMapper;
import com.trs.ai.moye.data.model.dao.BatchTaskTracerMapper;
import com.trs.ai.moye.data.model.dao.ProcessMapper;
import com.trs.ai.moye.data.model.dto.ConnectionStoragePoints;
import com.trs.ai.moye.data.model.dto.ExecuteParamDTO;
import com.trs.ai.moye.data.model.dto.StoragePointParams;
import com.trs.ai.moye.data.model.dto.StorageTable;
import com.trs.ai.moye.data.model.entity.BatchArrangement;
import com.trs.ai.moye.data.model.entity.BatchTaskRecord;
import com.trs.ai.moye.data.model.entity.BatchTaskTracer;
import com.trs.ai.moye.data.model.entity.TracerData;
import com.trs.ai.moye.data.model.enums.ArrangeDisplayType;
import com.trs.ai.moye.data.model.enums.BatchTaskStatus;
import com.trs.ai.moye.data.model.request.BatchProcessDataListRequest;
import com.trs.ai.moye.data.model.request.BatchTaskLogRequest;
import com.trs.ai.moye.data.model.request.DwdAddRequest;
import com.trs.ai.moye.data.model.request.DwdStartRequest;
import com.trs.ai.moye.data.model.request.ProcessRetryRequest;
import com.trs.ai.moye.data.model.request.SaveGovernanceInfoRequest;
import com.trs.ai.moye.data.model.request.StreamProcessDataListRequest;
import com.trs.ai.moye.data.model.request.fields.CheckExistedTableFieldsRequest;
import com.trs.ai.moye.data.model.response.BatchTaskLogResponse;
import com.trs.ai.moye.data.model.response.BatchTaskLogResponse.BatchTaskLogResponseItem;
import com.trs.ai.moye.data.model.response.BatchTaskRecordResponse;
import com.trs.ai.moye.data.model.response.CheckExistedTableFieldsResponse;
import com.trs.ai.moye.data.model.response.DwdAddResponse;
import com.trs.ai.moye.data.model.response.DwdExecuteScheduleResponse;
import com.trs.ai.moye.data.model.response.ProcessFlowResponse;
import com.trs.ai.moye.data.model.response.ProcessInfoVO;
import com.trs.ai.moye.data.model.response.ProcessMonitorDataValue;
import com.trs.ai.moye.data.model.response.ProcessMonitorDetailsResponse;
import com.trs.ai.moye.data.model.response.ProcessRetryResponse;
import com.trs.ai.moye.data.model.response.StreamProcessDataResponse;
import com.trs.ai.moye.data.model.service.DataModelScheduleService;
import com.trs.ai.moye.data.model.service.DataModelService;
import com.trs.ai.moye.data.model.service.DataProcessMonitorConfigService;
import com.trs.ai.moye.data.model.service.DwdModelService;
import com.trs.ai.moye.data.model.task.start.TaskStart;
import com.trs.ai.moye.data.standard.dao.DataStandardFieldMapper;
import com.trs.ai.moye.data.standard.dao.MetaDataStandardFieldMapper;
import com.trs.ai.moye.data.standard.dao.StructMetaDataFieldMapper;
import com.trs.ai.moye.data.standard.entity.StructMetaDataField;
import com.trs.ai.moye.minio.starter.properties.MinioProperties;
import com.trs.ai.moye.storageengine.service.StorageEngineService;
import com.trs.ai.moye.streamengine.feign.StreamEngineFeign;
import com.trs.ai.moye.xxljob.XXLJobManager;
import com.trs.ai.moye.xxljob.service.XXLJobApiService;
import com.trs.moye.ability.entity.InputBind;
import com.trs.moye.ability.request.RetryOperatorRequest;
import com.trs.moye.ability.response.RetryOperatorResponse;
import com.trs.moye.base.common.enums.ModelLayer;
import com.trs.moye.base.common.exception.BizException;
import com.trs.moye.base.common.log.enums.DataTracerTypeEnum;
import com.trs.moye.base.common.request.PageParams;
import com.trs.moye.base.common.request.SearchParams;
import com.trs.moye.base.common.request.TimeRangeParams;
import com.trs.moye.base.common.response.PageResponse;
import com.trs.moye.base.common.response.ResponseMessage;
import com.trs.moye.base.common.utils.AssertUtils;
import com.trs.moye.base.common.utils.DateTimeUtils;
import com.trs.moye.base.common.utils.JsonUtils;
import com.trs.moye.base.data.connection.dao.DataConnectionMapper;
import com.trs.moye.base.data.connection.entity.DataConnection;
import com.trs.moye.base.data.connection.enums.ConnectionType;
import com.trs.moye.base.data.connection.enums.SourceStructureType;
import com.trs.moye.base.data.execute.ExecuteModeEnum;
import com.trs.moye.base.data.model.constant.DataModelingConstants;
import com.trs.moye.base.data.model.dao.BusinessCategoryMapper;
import com.trs.moye.base.data.model.dao.DataModelExecuteConfigMapper;
import com.trs.moye.base.data.model.dao.DataModelFieldMapper;
import com.trs.moye.base.data.model.dao.DataModelMapper;
import com.trs.moye.base.data.model.dao.DataModelScheduleConfigMapper;
import com.trs.moye.base.data.model.entity.BusinessCategory;
import com.trs.moye.base.data.model.entity.DataModel;
import com.trs.moye.base.data.model.entity.DataModelExecuteConfig;
import com.trs.moye.base.data.model.entity.DataModelField;
import com.trs.moye.base.data.model.entity.DataModelScheduleConfig;
import com.trs.moye.base.data.model.entity.DataSourceConfig;
import com.trs.moye.base.data.model.enums.ModelExecuteStatus;
import com.trs.moye.base.data.model.field.mapping.FieldMappingResponse;
import com.trs.moye.base.data.model.field.mapping.MoyeFieldResponse;
import com.trs.moye.base.data.source.dao.DataSourceConfigMapper;
import com.trs.moye.base.data.standard.entity.MetaDataStandardField;
import com.trs.moye.base.data.storage.DataStorage;
import com.trs.moye.base.data.storage.IncrementInfo;
import com.trs.moye.base.xxljob.XxlJobInfo;
import io.minio.MinioClient;
import io.minio.errors.ErrorResponseException;
import io.minio.errors.InsufficientDataException;
import io.minio.errors.InternalException;
import io.minio.errors.InvalidResponseException;
import io.minio.errors.MinioException;
import io.minio.errors.ServerException;
import io.minio.errors.XmlParserException;
import java.io.BufferedReader;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.security.GeneralSecurityException;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 要素库接口实现类
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024/10/12 10:41
 **/
@Service
@Slf4j
public class DwdModelServiceImpl implements DwdModelService {

    @Resource
    private DataModelMapper dataModelMapper;

    @Resource
    private TaskStart taskStart;

    @Resource
    private BatchTaskRecordMapper batchTaskRecordMapper;

    @Resource
    private BatchTaskTracerMapper batchTaskTracerMapper;

    @Resource
    private ProcessMapper processMapper;

    @Resource
    private DataModelService dataModelService;

    @Resource
    private DataSourceConfigMapper dataSourceConfigMapper;

    @Resource
    private MetaDataStandardFieldMapper metaDataStandardFieldMapper;

    @Resource
    private BatchArrangementMapper batchArrangementMapper;

    @Resource
    private DataModelFieldMapper dataModelFieldMapper;

    @Resource
    private DataStandardFieldMapper dataStandardFieldMapper;

    @Resource
    private DataModelExecuteConfigMapper dataModelExecuteConfigMapper;

    @Resource
    private MinioClient minioClient;

    @Resource
    private MinioProperties minioProperties;

    @Resource
    private StorageEngineService storageEngineService;

    @Resource
    private DataConnectionMapper dataConnectionMapper;

    @Resource
    private CheckTableFieldsStrategyFactory strategyFactory;

    @Resource
    private DataModelScheduleService dataModelScheduleService;

    @Resource
    private DataModelScheduleConfigMapper dataModelScheduleConfigMapper;

    @Resource
    private XXLJobManager xxlJobManager;

    @Resource
    private NoticeSendConfInsideMapper noticeSendConfInsideMapper;

    @Resource
    private BusinessCategoryMapper businessCategoryMapper;

    @Resource
    private XXLJobApiService xxlJobApiService;

    @Resource
    private DataProcessMonitorConfigService dataProcessMonitorConfigService;

    @Resource
    private StreamEngineFeign streamEngineFeign;

    @Resource
    private StructMetaDataFieldMapper structMetaDataFieldMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public DwdAddResponse addModel(DwdAddRequest request) {
        if (Boolean.TRUE.equals(dataModelMapper.existsByEnName(request.getBasicInfo().getEnName()))) {
            throw new BizException(String.format("数据建模英文名称[%s]已存在", request.getBasicInfo().getEnName()));
        }
        //新增建模，创建dataModel
        DataModel dataModel = request.toModel();
        dataModelMapper.insert(dataModel);
        Integer dataModelId = dataModel.getId();
        // 保存数据来源
        createDataSources(dataModelId, request.getDataSourceIds());

        //创建后台消息中心日志推送配置
        BusinessCategory businessCategory = businessCategoryMapper.selectById(request.getBusinessCategoryId());
        noticeSendConfInsideMapper.insert(NoticeSendConfInside.formSystemAdd(dataModel, businessCategory));

        //如果选择已有的表来作为存储
        if (request.getStoragePointList().stream().anyMatch(e -> ObjectUtils.isNotEmpty(e.getTableNameList()))) {
            addStorageInfo(request.getStoragePointList().stream().map(ConnectionStoragePoints::new).toList(),
                dataModel);
        } else {
            //保存存储信息
            List<Integer> connectionIds = request.getStoragePointList().stream()
                .map(StoragePointParams::getConnectionId).toList();
            dataModelService.createDataStorages(dataModel, connectionIds);
        }
        return new DwdAddResponse(dataModelId, request.getModelLayer());
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void syncFieldsFromMetaDataStandard(Integer dataModelId, Integer metaDataStandardId) {

        DataModel dataModel = dataModelMapper.getById(dataModelId);
        if (Objects.isNull(dataModel)) {
            throw new BizException("数据建模【id:{}】不存在", dataModelId);
        }

        List<Integer> connectionIds = dataModel.getDataStorages().stream()
            .map(DataStorage::getConnectionId).toList();

        // 根据存储连接类型确定字段同步策略
        SourceStructureType storageStructureType = getStorageStructureType(connectionIds);
        createModelFieldsFromMetaDataStandard(dataModelId, metaDataStandardId, storageStructureType);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveGovernanceInfo(Integer dataModelId, SaveGovernanceInfoRequest request) {
        ExecuteModeEnum executeMode = request.getExecuteMode();
        DataModel dataModel = dataModelMapper.getById(dataModelId);

        if (Objects.isNull(dataModel)) {
            throw new BizException("数据建模【id:{}】不存在", dataModelId);
        }

        // 保存默认监控信息
        if (!ExecuteModeEnum.REALTIME.equals(executeMode)) {
            dataModelService.createDefaultTaskExecutionTimeConfig(dataModelId, executeMode);
        }

        // 保存调度信息
        dataModelScheduleService.createScheduleConfig(dataModel, request.getScheduleInfo());
        createExecuteConfig(dataModelId, executeMode);

        // 存储dag和code模式
        createBatchArrangement(dataModelId, request.getProcessingMode());

        // 处理实时流处理模式的特殊逻辑
        boolean isRealtimeWithoutMetaDataStandard = ExecuteModeEnum.REALTIME.equals(executeMode)
            && Objects.isNull(dataModel.getMetaDataStandardId());

        if (isRealtimeWithoutMetaDataStandard) {
            // 新增审计字段，只有流处理要默认加上审计字段
            createBuiltInFields(dataModelId);

            // 从贴源库同步字段，如果数据建模已经使用了元数据标准，就不再从贴源库进行字段同步
            request.getDataSourceIds().forEach(modelId -> processRealTimeFields(dataModelId, modelId));
        }
    }

    private SourceStructureType getStorageStructureType(List<Integer> connectionIds) {
        List<ConnectionType> connectionTypes = dataConnectionMapper.selectByIds(connectionIds).stream()
            .map(DataConnection::getConnectionType).toList();
        //如果connectionTypes列表中没有SourceStructureType是VIEW的，则返回一个SourceStructureType.STRUCTURED
        if (connectionTypes.isEmpty() || connectionTypes.stream()
            .noneMatch(e -> e.getSourceStructureType() == SourceStructureType.VIEW)) {
            return SourceStructureType.STRUCTURED;
        } else {
            return SourceStructureType.VIEW;
        }
    }

    @Override
    public void addStorageInfo(List<ConnectionStoragePoints> connectionStoragePointsList, DataModel dataModel) {
        //通过链接ConnectionId和表名获取字段
        //将字段同步至data_model_field，并更改DataStorages信息
        Map<String, MoyeFieldResponse> uniqueFields = collectUniqueFields(connectionStoragePointsList);
        dataModelService.updateDataModelFields(dataModel.getId(), new ArrayList<>(uniqueFields.values()), dataModel,
            dataModel.getCreateMode());
        createDataStorages(connectionStoragePointsList, dataModel);
    }

    private Map<String, MoyeFieldResponse> collectUniqueFields(
        List<ConnectionStoragePoints> connectionStoragePointsList) {
        // 使用 LinkedHashMap 保留字段顺序并去重
        Map<String, MoyeFieldResponse> uniqueFields = new LinkedHashMap<>();
        for (ConnectionStoragePoints connectionStoragePoints : connectionStoragePointsList) {
            List<StorageTable> storageTables = connectionStoragePoints.getStorageTables();
            if (ObjectUtils.isEmpty(storageTables)) {
                continue;
            }
            Integer connectionId = connectionStoragePoints.getConnectionId();
            DataConnection connection = dataConnectionMapper.selectById(connectionId);
            for (StorageTable storageTable : storageTables) {
                String storageTableName = storageTable.getTableName();
                if (StringUtils.isBlank(storageTableName)) {
                    continue;
                }
                // 获取数据库表字段信息
                FieldMappingResponse dbTableFields;
                switch (connection.getConnectionType().getCategory()) {
                    case DATA_BASE -> dbTableFields = storageEngineService.getDbTableFields(connectionId,
                        storageTableName);
                    case MQ -> dbTableFields = storageEngineService.getMqTableFields(connectionId, storageTableName);
                    default -> throw new BizException(
                        "不支持的获取表字段的连接分类：" + connection.getConnectionType().getCategory());
                }
                if (ObjectUtils.isNotEmpty(dbTableFields.getModelFields())) {
                    for (MoyeFieldResponse field : dbTableFields.getModelFields()) {
                        uniqueFields.putIfAbsent(field.getEnName(), field);
                    }
                }
            }
        }
        return uniqueFields;
    }

    private void createDataStorages(List<ConnectionStoragePoints> connectionStoragePointsList, DataModel dataModel) {
        // 获取所有连接信息并缓存
        Map<Integer, DataConnection> connectionCache = buildConnectionCache(connectionStoragePointsList);

        // 遍历存储点，创建数据存储信息
        for (ConnectionStoragePoints connectionStoragePoints : connectionStoragePointsList) {
            Integer connectionId = connectionStoragePoints.getConnectionId();
            DataConnection dataConnection = connectionCache.get(connectionId);
            List<StorageTable> storageTables = connectionStoragePoints.getStorageTables();
            if (Objects.isNull(dataConnection) || ObjectUtils.isEmpty(storageTables)) {
                continue;
            }
            for (StorageTable storageTable : storageTables) {
                String storageTableName = storageTable.getTableName();
                if (StringUtils.isBlank(storageTableName)) {
                    continue;
                }
                // 创建数据存储信息
                dataModelService.createExistDataStorage(dataModel, dataConnection, storageTable);
            }
        }
    }

    @NotNull
    private Map<Integer, DataConnection> buildConnectionCache(
        List<ConnectionStoragePoints> connectionStoragePointsList) {
        // 如果 connectionStoragePointsList 为空，直接返回空 map
        if (ObjectUtils.isEmpty(connectionStoragePointsList)) {
            return Collections.emptyMap();
        }
        // 提取所有 connectionId
        Set<Integer> connectionIds = connectionStoragePointsList.stream()
            .map(ConnectionStoragePoints::getConnectionId)
            .collect(Collectors.toSet());
        // 如果 connectionIds 为空，返回空 map
        if (ObjectUtils.isEmpty(connectionIds)) {
            return Collections.emptyMap();
        }
        // 批量查询 connectionId 对应的 DataConnection 并构建 map
        return dataConnectionMapper.selectByIds(connectionIds).stream()
            .collect(Collectors.toMap(DataConnection::getId, Function.identity()));
    }

    private void createExecuteConfig(Integer dataModelId, ExecuteModeEnum executeMode) {
        DataModelExecuteConfig executeConfig = new DataModelExecuteConfig();
        executeConfig.setDataModelId(dataModelId);
        //流处理类型，插入默认增量信息，以使seatunnel以append模式接入数据
        if (ExecuteModeEnum.REALTIME.equals(executeMode)) {
            executeConfig.setIncrementInfo(new IncrementInfo());
        }
        dataModelExecuteConfigMapper.insert(executeConfig);
    }

    private void createBuiltInFields(Integer dataModelId) {
        List<DataModelField> fieldsToInsert = dataStandardFieldMapper.listBuiltInFields().stream()
            .filter(builtInField -> !builtInField.getEnName().equals(TRS_MOYE_FTP_FILE_NAME))
            .map(builtInField -> {
                DataModelField field = builtInField.toDataModelField();
                field.setProvideService(false);
                field.setDataModelId(dataModelId);
                return field;
            }).toList();
        dataModelFieldMapper.insert(fieldsToInsert);
    }

    private void createBatchArrangement(Integer dataModelId, ArrangeDisplayType processingMode) {
        if (Objects.nonNull(processingMode)) {
            BatchArrangement batchArrangement = new BatchArrangement();
            batchArrangement.setDisplayType(processingMode);
            batchArrangement.setDataModelId(dataModelId);
            batchArrangementMapper.insert(batchArrangement);
        }
    }

    @Override
    public void createDataSources(Integer dataModelId, List<Integer> sourceModelIds) {
        List<DataModel> dataModels = dataModelMapper.selectByIds(sourceModelIds);
        List<DataSourceConfig> dataSourceConfigs = new ArrayList<>();
        //先获取dataModel的调度模式，如果是实时流处理数据来源就是依赖的建模的来源

        dataSourceConfigs = dataModels.stream().map(dm -> DataSourceConfig.from(dm, dataModelId)).toList();

        dataSourceConfigMapper.insert(dataSourceConfigs);
    }


    /**
     * 从 元数据标准 同步 字段 到 数据建模
     *
     * @param dataModelId        数据建模id
     * @param metaDataStandardId 元数据标准id
     * @param type               连接数据库类型
     */
    @Override
    public void createModelFieldsFromMetaDataStandard(Integer dataModelId, Integer metaDataStandardId,
        SourceStructureType type) {
        List<DataModelField> dataModelFields = new ArrayList<>();
        //图元数据和结构化元数据分开
        if (type.equals(SourceStructureType.VIEW)) {
            List<MetaDataStandardField> metaDataStandardFields = metaDataStandardFieldMapper.selectByMetaDataStandardId(
                metaDataStandardId);
            dataModelFields = metaDataStandardFields.stream().map(metaDataStandardField -> {
                DataModelField dataModelField = new DataModelField(metaDataStandardField);
                dataModelField.setDataModelId(dataModelId);
                return dataModelField;
            }).toList();
        } else {
            List<StructMetaDataField> structMetaDataFields = structMetaDataFieldMapper.selectByStructMetaDataId(
                metaDataStandardId);
            dataModelFields = structMetaDataFields.stream().map(structMetaDataField -> {
                DataModelField dataModelField = structMetaDataField.fromDataModelField();
                dataModelField.setDataModelId(dataModelId);
                return dataModelField;
            }).toList();
        }
        if (!dataModelFields.isEmpty()) {
            dataModelFieldMapper.insert(dataModelFields);
        }
        dataModelMapper.updateIsSyncProperty(dataModelId, Boolean.TRUE);
    }


    /**
     * 将贴源库字段同步过来
     *
     * @param dataModelId       现有要素库建模ID
     * @param dataModelSourceId 所依赖的数据建模ID
     * <AUTHOR>
     * @since 2024/10/10 10:05
     */
    private void processRealTimeFields(Integer dataModelId, Integer dataModelSourceId) {
        //同步字段 流处理从贴源库的字段同步
        DataModel dataModel = dataModelMapper.getById(dataModelSourceId);
        //如果来源是贴源库并且没有同步字段
        if (Objects.nonNull(dataModel)) {
            ModelLayer layer = dataModel.getLayer();
            if (ModelLayer.ODS.equals(layer) && !dataModel.isSyncField()) {
                try {
                    dataModelService.syncOdsFields(dataModelSourceId);
                } catch (BizException e) {
                    log.error("同步贴源库字段失败!");
                }
            }
        }
        List<DataModelField> dataModelFieldList = dataModelFieldMapper.selectByDataModelId(dataModelSourceId);
        if (!dataModelFieldList.isEmpty()) {
            List<DataModelField> dataModelFields = new ArrayList<>();
            for (DataModelField dataModelField : dataModelFieldList) {
                dataModelField.setDataModelId(dataModelId);
                dataModelField.setId(null);
                if (!DataModelingConstants.isAuditField(dataModelField.getEnName())) {
                    dataModelFields.add(dataModelField);
                }
            }
            //添加字段
            addProperties(dataModelId, dataModelFields);
            //设置已同步字段
            dataModelMapper.updateIsSyncProperty(dataModelId, Boolean.TRUE);
        }
    }

    private void addProperties(Integer dataModelId, List<DataModelField> dataModelFields) {
        try {
            //先查询出已有的字段
            List<DataModelField> existDataModelFields = dataModelFieldMapper.selectByDataModelId(dataModelId);
            //如果有重复的就不同步
            for (DataModelField existDataModelField : existDataModelFields) {
                //重复就移除
                dataModelFields.removeIf(
                    dataModelField -> existDataModelField.getEnName().equals(dataModelField.getEnName())
                        && existDataModelField.getType().equals(dataModelField.getType())
                        && existDataModelField.getZhName().equals(dataModelField.getZhName()));
            }
            //插入新的字段
            dataModelFieldMapper.insert(dataModelFields);
        } catch (Exception e) {
            log.error("同步字段失败!", e);
            throw new BizException(String.format("%s字段同步字段失败！", ModelLayer.ODS.getLabel()), e);
        }
    }

    @Transactional(rollbackFor = Throwable.class)
    @Override
    public void startTask(Integer id, DwdStartRequest dwdStartRequest) {
        BatchArrangement batchArrangement = batchArrangementMapper.selectByDataModelId(id);
        AssertUtils.notEmpty(batchArrangement, "主键为【%s】的建模缺少编排信息，不允许启动");
        if (batchArrangement.getDisplayType() == ArrangeDisplayType.CODE) {
            batchArrangement.setIsUpdatedTasks(false);
            batchArrangementMapper.updateById(batchArrangement);
        }
        ExecuteParamDTO executeParamDTO = new ExecuteParamDTO(dwdStartRequest);
        taskStart.startTask(id, executeParamDTO);
        dataProcessMonitorConfigService.updateXxlJobStatus(id, ModelExecuteStatus.START);

    }

    @Transactional(rollbackFor = Throwable.class)
    @Override
    public void stopTask(Integer id) {
        taskStart.stopTask(id);
        dataProcessMonitorConfigService.updateXxlJobStatus(id, ModelExecuteStatus.STOP);
    }

    @Transactional(rollbackFor = Throwable.class)
    @Override
    public void pauseTask(Integer id) {
        taskStart.pauseOdsTask(id);
        dataProcessMonitorConfigService.updateXxlJobStatus(id, ModelExecuteStatus.PAUSE);
    }

    @Override
    public PageResponse<BatchTaskRecordResponse> getBatchProcessDataList(BatchProcessDataListRequest request) {
        PageParams pageParams = request.getPageParams();
        TimeRangeParams timeRangeParams = request.getTimeRangeParams();
        BatchTaskStatus taskStatus = request.getTaskStatus();
        Integer modelId = request.getModelId();
        SearchParams searchParams = request.getSearchParams();
        Page<BatchTaskRecord> page = batchTaskRecordMapper.selectBatchTaskMonitorList(timeRangeParams, taskStatus,
            modelId,
            searchParams, pageParams.toPage());
        List<BatchTaskRecordResponse> items = page.getRecords().stream()
            .map(batchTaskRecord -> BatchTaskRecordResponse.from(batchTaskRecord, batchTaskTracerMapper)).toList();
        return PageResponse.of(items, (int) page.getCurrent(), page.getTotal(), (int) page.getSize());
    }


    @Override
    public PageResponse<StreamProcessDataResponse> getStreamProcessDataList(StreamProcessDataListRequest request) {
        PageParams pageParams = request.getPageParams();
        TimeRangeParams timeRangeParams = request.getTimeRangeParams();
        String beginTime = timeRangeParams.getMinTimeStr();
        String endTime = timeRangeParams.getMaxTimeStr();
        String operator = null;
        if (request.getOperator() != null) {
            operator = request.getOperator().getOperator();
        }
        String conditionKey = request.getConditionKey();
        Object conditionValue = request.getConditionValue();
        Integer modelId = request.getModelId();
        Boolean isError = request.getIsError();
        SearchParams searchParams = request.getSearchParams();
        Page<StreamProcessDataResponse> streamProcessDataResponsePage = processMapper.selectStreamTaskMonitorList(
            beginTime, endTime, operator, conditionValue, conditionKey, modelId, isError, pageParams.toPage(),
            searchParams);
        return PageResponse.of(streamProcessDataResponsePage);

    }

    @Override
    public List<BatchTaskTracer> getBatchTaskRecordTracerList(String executeId) {
        return batchTaskTracerMapper.listByExecuteIdOrderByStartTimeAsc(executeId);
    }

    @Override
    public ProcessInfoVO getProcessInfo(String recordId, String processId) {
        // 查找监控数据
        TracerData tracerData = processMapper.selectTracerDataByProcessId(recordId, processId);

        //找到对应元数据
        DataModel element = dataModelMapper.selectById(tracerData.getDataSourceId());

        // 要素库的来源
        List<DataModel> odsList = new ArrayList<>();
        if (Objects.nonNull(element) && Objects.nonNull(element.getId())) {
            List<Integer> dataSourceModelIds = dataSourceConfigMapper.selectByDataModel(element.getId()).stream()
                .map(DataSourceConfig::getSourceModelId).distinct().toList();
            if (!dataSourceModelIds.isEmpty()) {
                odsList = dataModelMapper.selectByIds(dataSourceModelIds);
            }
        }
        ProcessInfoVO processInfoResponse = new ProcessInfoVO(tracerData);
        if (!odsList.isEmpty()) {
            // 实时处理的要素库的来源只有一个
            processInfoResponse.setDataSourceName(odsList.get(0).getZhName());
        }
        return processInfoResponse;
    }

    @Override
    public List<ProcessFlowResponse> getProcessFlow(Integer dataModelId, String recordId) {

        List<ProcessFlowResponse> responses = new ArrayList<>();
        ProcessFlowResponse accessMaster = getProcessFlowResponse(dataModelId, recordId,
            DataTracerTypeEnum.DATA_ACCESS);
        ProcessFlowResponse processingMaster = getProcessFlowResponse(dataModelId, recordId,
            DataTracerTypeEnum.DATA_PROCESSING);
        ProcessFlowResponse storageMaster = getProcessFlowResponse(dataModelId, recordId,
            DataTracerTypeEnum.DATA_STORAGE);
        responses.add(accessMaster);
        responses.add(processingMaster);
        if (!storageMaster.getChildren().isEmpty()) {
            responses.add(storageMaster);
        }
        return responses;
    }

    private ProcessFlowResponse getProcessFlowResponse(Integer dataModelId, String recordId,
        DataTracerTypeEnum dataTracerTypeEnum) {
        List<TracerData> accessTracerDataList = processMapper.selectTracerDataListByRecordId(dataModelId, recordId,
            dataTracerTypeEnum.getNodeType());
        List<ProcessFlowResponse> access = accessTracerDataList.stream()
            .map(tracerData -> new ProcessFlowResponse(tracerData, dataTracerTypeEnum.getNodeType())).toList();
        ProcessFlowResponse accessMaster = new ProcessFlowResponse();
        accessMaster.setChildren(access);
        accessMaster.setTitle(dataTracerTypeEnum.getNodeName());
        accessMaster.setDuration(getDuration(accessTracerDataList));
        return accessMaster;
    }

    private Long getDuration(List<TracerData> tracerDataList) {
        Long durationSum = 0L;
        for (TracerData tracerDataAcc : tracerDataList) {
            durationSum += tracerDataAcc.getProcessingTime();
        }
        return durationSum;
    }


    @Override
    public ProcessMonitorDetailsResponse getMonitorDetails(String recordId, String processId) {
        return getMonitorDetails(processMapper.selectTracerDataByProcessId(recordId, processId));
    }

    private ProcessMonitorDetailsResponse getMonitorDetails(TracerData tracerData) {
        ProcessMonitorDetailsResponse response = new ProcessMonitorDetailsResponse();
        Integer dataSourceId = tracerData.getDataSourceId();
        ObjectNode result = JsonUtils.toJsonObject(tracerData.getResults());
        if (!tracerData.getProcessingType().equals(DataTracerTypeEnum.DATA_STORAGE.getNodeType())) {
            ObjectNode msgJson = JsonUtils.toJsonObject(tracerData.getMsgContent());
            response.setOrigin(getMonitorDataValues(msgJson, dataSourceId));
        }
        response.setResult(getMonitorDataValues(result, dataSourceId));
        return response;
    }

    private List<ProcessMonitorDataValue> getMonitorDataValues(ObjectNode content, Integer dataSourceId) {
        List<ProcessMonitorDataValue> dataValues = new ArrayList<>();
        if (Objects.isNull(content)) {
            return dataValues;
        }
        List<DataSourceConfig> properties = dataSourceConfigMapper.selectByDataModel(dataSourceId);
        List<DataModelField> dataModelFields = dataModelFieldMapper.selectByDataModelId(dataSourceId);

        content.fields().forEachRemaining(entry -> {
            String key = entry.getKey();
            Object value = entry.getValue();

            List<DataSourceConfig> cnNameList = properties.stream().filter(e -> e.getEnName().equals(key)).toList();

            List<DataModelField> templateCnNameList = dataModelFields.stream().filter(e -> e.getEnName().equals(key))
                .toList();

            ProcessMonitorDataValue dataValue = new ProcessMonitorDataValue();

            if (!cnNameList.isEmpty()) {
                dataValue.setZhName(cnNameList.get(0).getZhName());
            } else if (!templateCnNameList.isEmpty()) {
                dataValue.setZhName(templateCnNameList.get(0).getZhName());
            }

            dataValue.setEnName(key);
            dataValue.setValue(value.toString());

            dataValues.add(dataValue);
        });

        return dataValues;
    }


    @Override
    public ProcessRetryResponse retryProcess(ProcessRetryRequest request) {
        ProcessRetryResponse response = new ProcessRetryResponse(request);

        // 获取dataSourceId, operatorId
        TracerData tracerData = processMapper.selectTracerDataByProcessId(response.getRecordId(),
            response.getProcessId());
        Integer dataModelId = tracerData.getDataSourceId();
        Integer operatorId = tracerData.getOperatorId();
        Map<String, Object> origin = InputBind.restoreOriginalParams(
            JsonUtils.jsonStringToMap(tracerData.getMsgContent()));
        Long recordId = tracerData.getRecordId();

        // 请求服务
        RetryOperatorRequest retryOperatorRequest = new RetryOperatorRequest(dataModelId, operatorId, recordId, origin);
        ResponseMessage responseMessage = streamEngineFeign.retryOperator(retryOperatorRequest);
        ResponseMessage resultResponse = JsonUtils.parseObject(
            JsonUtils.toJsonString(responseMessage.getData()), ResponseMessage.class);
        if (Objects.isNull(resultResponse)) {
            response.setIsException(true);
            response.setException("stream-engine返回结果为空！");
            return response;
        }
        if (!resultResponse.isSuccess()) {
            response.setIsException(false);
            response.setException(resultResponse.getMessage());
            return response;
        }
        RetryOperatorResponse retryOperatorResponse = JsonUtils.parseObject(
            JsonUtils.toJsonString(resultResponse.getData()), RetryOperatorResponse.class);
        if (Objects.nonNull(retryOperatorResponse)) {
            response.setStartTime(retryOperatorResponse.getStartTime());
            response.setDuration(retryOperatorResponse.getDuration());
            response.setIsException(retryOperatorResponse.getIsException());
            response.setException(retryOperatorResponse.getException());
            ObjectNode jsonNodes = JsonUtils.toJsonObject(retryOperatorResponse.getResult());
            if (Objects.nonNull(jsonNodes) && !jsonNodes.isEmpty()) {
                response.setResult(getMonitorDataValues(jsonNodes, dataModelId));
            }
        }
        return response;
    }

    @Override
    public List<BatchTaskLogResponse> getBatchLogs(BatchTaskLogRequest request) {
        try {
            // 获取 minio 上对象名
            List<String> pathList = getBatchLogObjects(request);

            List<BatchTaskLogResponseItem> batchTaskLogResponseItems = pathList.parallelStream().map(path -> {
                // 检查流中是否包含ERROR
                boolean hasError = false;
                try (InputStream inputStream = MinioUtil.getObjectStream(path, minioClient,
                    minioProperties.getBucket().getLogs())) {
                    hasError = checkStreamForError(inputStream);
                } catch (Exception e) {
                    log.error("从minio[url:{} bucket:{}]查询对象[{}]异常，错误信息：{}",
                        minioProperties.getEndpoint(), minioProperties.getBucket().getLogs(), path,
                        e.getLocalizedMessage(), e);
                }
                return BatchTaskLogResponseItem.fromPath(path, hasError);
            }).toList();

            return BatchTaskLogResponse.from(request.getTaskId(), request.getExecuteId(), batchTaskLogResponseItems);
        } catch (MinioException | IOException | GeneralSecurityException e) {
            String errorMsg = String.format("从minio[url:%s bucket:%s]查询yarn日志[applicationId:%s]异常，错误信息：%s",
                minioProperties.getEndpoint(), minioProperties.getBucket().getLogs(), request,
                e.getLocalizedMessage());
            log.error(errorMsg, e);
            throw new BizException(errorMsg, e);
        }
    }

    /**
     * 获取批处理日志对象名
     *
     * @param request 前端请求
     * @return 日志对象名列表
     * @throws ServerException           服务端异常
     * @throws InsufficientDataException 数据不足异常
     * @throws ErrorResponseException    错误响应异常
     * @throws IOException               IO异常
     * @throws NoSuchAlgorithmException  无算法异常
     * @throws InvalidKeyException       无效密钥异常
     * @throws InvalidResponseException  无效响应异常
     * @throws XmlParserException        XML解析异常
     * @throws InternalException         内部异常
     */
    @Override
    public List<String> getBatchLogObjects(BatchTaskLogRequest request)
        throws ServerException, InsufficientDataException, ErrorResponseException, IOException, NoSuchAlgorithmException, InvalidKeyException, InvalidResponseException, XmlParserException, InternalException {
        return MinioUtil.getObjectsNameByDir(
            request.getTaskId() + File.separator + request.getExecuteId(),
            minioClient,
            minioProperties.getBucket().getLogs());
    }

    /**
     * 检查流中是否包含ERROR
     *
     * @param stream 流
     * @return boolean
     */
    private boolean checkStreamForError(InputStream stream) throws IOException {
        try (BufferedReader reader = new BufferedReader(
            new InputStreamReader(stream, StandardCharsets.UTF_8))) {
            String line;
            // 匹配前后有空白或字符串边界的ERROR单词
            java.util.regex.Pattern pattern = java.util.regex.Pattern.compile("(?:\\s|^)ERROR(?:\\s|$)");
            while ((line = reader.readLine()) != null) {
                if (pattern.matcher(line).find()) {
                    return true;
                }
            }
            return false;
        }
    }


    @Override
    public String getLogFileByLogPath(String path) {
        try {
            byte[] object = MinioUtil.getFileFromBucket(path, minioClient, minioProperties.getBucket().getLogs());
            return new String(object, StandardCharsets.UTF_8);
        } catch (Exception e) {
            String errorMsg = String.format("从minio[url:%s bucket:%s]查询日志文件[path:%s]异常，错误信息：%s",
                minioProperties.getEndpoint(), minioProperties.getBucket().getLogs(), path, e.getLocalizedMessage());
            log.error(errorMsg, e);
            throw new BizException(errorMsg, e);
        }

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateDataSource(Integer dataModelId, List<Integer> dataSourceIds) {
        dataSourceConfigMapper.deleteByDataModelId(dataModelId);
        // 可更新数据来源的一定是批处理代码模式，因此写死调度模式
        createDataSources(dataModelId, dataSourceIds);
    }


    @Override
    public CheckExistedTableFieldsResponse checkExistedTableFields(CheckExistedTableFieldsRequest request) {
        // 获取数据来源字段
        CheckTableFieldsStrategy strategy = strategyFactory.getStrategy(request);
        return strategy.checkExistedTableFields(request);
    }

    @Override
    public DwdExecuteScheduleResponse getModelExecuteScheduleInfo(Integer dataModelId) {
        // 检查数据模型是否存在
        DataModel dataModel = dataModelMapper.getById(dataModelId);
        if (Objects.isNull(dataModel)) {
            throw new BizException("数据建模【id:{}】不存在", dataModelId);
        }
        LocalDateTime nextExecuteTime;
        Optional<Integer> xxlJobId = Optional.ofNullable(dataModelScheduleConfigMapper.selectByDataModelId(dataModelId))
            .map(DataModelScheduleConfig::getXxlJobId);
        if (ModelExecuteStatus.START.equals(dataModel.getExecuteStatus())) {
            nextExecuteTime = xxlJobId.map(xxlJobApiService::getJobInfo)
                .map(XxlJobInfo::getTriggerNextTime)
                .map(DateTimeUtils::parse)
                .orElse(null);
        } else {
            nextExecuteTime = xxlJobId.map(xxlJobManager::getNextExecuteTime)
                .map(this::parseFirstValidTime)
                .orElse(null);
        }
        return new DwdExecuteScheduleResponse(nextExecuteTime);
    }

    private LocalDateTime parseFirstValidTime(List<String> times) {
        if (ObjectUtils.isEmpty(times) || StringUtils.isBlank(times.get(0))) {
            return null;
        }
        try {
            return DateTimeUtils.parse(times.get(0));
        } catch (Exception e) {
            log.warn("Failed  to parse next execute time: {}", times.get(0), e);
            return null;
        }
    }

}
