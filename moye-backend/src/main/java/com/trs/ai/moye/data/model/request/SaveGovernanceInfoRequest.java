package com.trs.ai.moye.data.model.request;

import com.trs.ai.moye.data.model.enums.ArrangeDisplayType;
import com.trs.moye.base.data.execute.ExecuteModeEnum;
import java.util.List;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import lombok.Data;
import org.springframework.validation.annotation.Validated;

/**
 * 保存数据治理相关信息请求
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024/12/20 15:11:22
 */
@Data
@Validated
public class SaveGovernanceInfoRequest {

    /**
     * 执行模式
     */
    @NotNull(message = "执行模式不能为空")
    private ExecuteModeEnum executeMode;

    /**
     * 调度信息
     */
    private ModelScheduleConfigRequest scheduleInfo;

    /**
     * 处理模式
     */
    private ArrangeDisplayType processingMode;

    /**
     * 选择的数据来源源ID，这个地方是其他建模ID
     */
    @NotEmpty(message = "数据来源不能为空")
    private List<Integer> dataSourceIds;
}
