package com.trs.ai.moye.data.model.request;

import com.trs.ai.moye.data.model.dto.StoragePointParams;
import com.trs.moye.base.data.execute.ExecuteModeEnum;
import java.util.List;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import lombok.Data;
import org.springframework.validation.annotation.Validated;

/**
 * 从元数据标准同步字段请求
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024/12/20 15:11:22
 */
@Data
@Validated
public class SyncFieldsFromMetaDataStandardRequest {

    /**
     * 执行模式
     */
    @NotNull(message = "执行模式不能为空")
    private ExecuteModeEnum executeMode;

    /**
     * 元数据标准id
     */
    private Integer metaDataStandardId;

    /**
     * 选择的数据来源源ID，这个地方是其他建模ID
     */
    @NotEmpty(message = "数据来源不能为空")
    private List<Integer> dataSourceIds;

    /**
     * 选择的存储ID
     */
    @NotEmpty(message = "存储点列表不能为空")
    private List<StoragePointParams> storagePointList;
}
